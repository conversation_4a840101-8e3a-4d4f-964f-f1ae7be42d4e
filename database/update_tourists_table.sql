-- 更新游客表结构，添加缺失的字段
-- 执行此脚本前请备份数据库

USE tourism_system;

-- 检查并添加 customer_type 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'tourism_system'
    AND TABLE_NAME = 'tourists'
    AND COLUMN_NAME = 'customer_type'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tourists ADD COLUMN customer_type VARCHAR(50) COMMENT "客户类型：VIP客户、常客、新客户、商务客户、家庭客户"',
    'SELECT "customer_type column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 preferences 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'tourism_system'
    AND TABLE_NAME = 'tourists'
    AND COLUMN_NAME = 'preferences'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tourists ADD COLUMN preferences TEXT COMMENT "旅游偏好"',
    'SELECT "preferences column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 notes 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'tourism_system'
    AND TABLE_NAME = 'tourists'
    AND COLUMN_NAME = 'notes'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE tourists ADD COLUMN notes TEXT COMMENT "备注信息"',
    'SELECT "notes column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示更新后的表结构
DESCRIBE tourists;

-- 为现有数据设置默认的客户类型
UPDATE tourists SET customer_type = '新客户' WHERE customer_type IS NULL;

SELECT 'Tourist table structure updated successfully!' as result;
