-- 旅游行业管理与运行调度系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS tourism_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE tourism_system;

-- 1. 游客表
CREATE TABLE IF NOT EXISTS tourists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
    phone VARCHAR(11) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(200) COMMENT '地址',
    register_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    status VARCHAR(20) DEFAULT '活跃' COMMENT '状态：活跃、非活跃',
    remarks TEXT COMMENT '备注',
    customer_type VARCHAR(50) COMMENT '客户类型：VIP客户、常客、新客户、商务客户、家庭客户',
    preferences TEXT COMMENT '旅游偏好',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '游客信息表';

-- 2. 星级饭店表
CREATE TABLE IF NOT EXISTS hotels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '饭店名称',
    address VARCHAR(200) NOT NULL COMMENT '地址',
    phone VARCHAR(20) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    star_level INT NOT NULL CHECK (star_level BETWEEN 1 AND 5) COMMENT '星级：1-5星',
    room_count INT NOT NULL DEFAULT 0 COMMENT '房间数量',
    facilities TEXT COMMENT '设施描述',
    manager VARCHAR(50) COMMENT '负责人',
    status VARCHAR(20) DEFAULT '营业' COMMENT '状态：营业、停业、装修',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '星级饭店信息表';

-- 3. 旅行社表
CREATE TABLE IF NOT EXISTS travel_agencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '旅行社名称',
    address VARCHAR(200) NOT NULL COMMENT '地址',
    phone VARCHAR(20) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    license_number VARCHAR(50) NOT NULL UNIQUE COMMENT '营业执照号',
    manager VARCHAR(50) COMMENT '负责人',
    business_scope TEXT COMMENT '经营范围',
    status VARCHAR(20) DEFAULT '营业' COMMENT '状态：营业、停业',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '旅行社信息表';

-- 4. 旅游车辆表
CREATE TABLE IF NOT EXISTS vehicles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_plate VARCHAR(20) NOT NULL UNIQUE COMMENT '车牌号',
    vehicle_type VARCHAR(50) NOT NULL COMMENT '车辆类型',
    seat_count INT NOT NULL COMMENT '座位数',
    driver_name VARCHAR(50) COMMENT '司机姓名',
    driver_phone VARCHAR(11) COMMENT '司机电话',
    agency_id INT COMMENT '所属旅行社ID',
    status VARCHAR(20) DEFAULT '可用' COMMENT '状态：可用、使用中、维修中',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (agency_id) REFERENCES travel_agencies(id) ON DELETE SET NULL
) COMMENT '旅游车辆信息表';

-- 5. 导游表
CREATE TABLE IF NOT EXISTS guides (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
    phone VARCHAR(11) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    license_number VARCHAR(50) NOT NULL UNIQUE COMMENT '导游证号',
    languages VARCHAR(200) COMMENT '语言能力',
    experience INT DEFAULT 0 COMMENT '从业年限',
    specialties TEXT COMMENT '专业特长',
    status VARCHAR(20) DEFAULT '在职' COMMENT '状态：在职、离职、休假',
    hire_date DATE COMMENT '入职时间',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '导游信息表';

-- 6. 公寓表
CREATE TABLE IF NOT EXISTS apartments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '公寓名称',
    address VARCHAR(200) NOT NULL COMMENT '地址',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    room_type VARCHAR(50) NOT NULL COMMENT '房型',
    room_count INT NOT NULL DEFAULT 0 COMMENT '房间数量',
    price_per_night DECIMAL(10,2) NOT NULL COMMENT '每晚价格',
    facilities TEXT COMMENT '设施描述',
    manager VARCHAR(50) COMMENT '负责人',
    status VARCHAR(20) DEFAULT '可预订' COMMENT '状态：可预订、已满房、维修中、暂停营业',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '公寓信息表';

-- 7. 投诉表
CREATE TABLE IF NOT EXISTS complaints (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    customer_phone VARCHAR(20) NOT NULL COMMENT '客户电话',
    customer_email VARCHAR(100) COMMENT '客户邮箱',
    complaint_type VARCHAR(50) NOT NULL COMMENT '投诉类型',
    complaint_content TEXT NOT NULL COMMENT '投诉内容',
    related_service VARCHAR(100) COMMENT '相关服务',
    complaint_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '投诉时间',
    status VARCHAR(20) DEFAULT '待处理' COMMENT '状态：待处理、处理中、已解决、已关闭',
    priority VARCHAR(10) DEFAULT '普通' COMMENT '优先级：紧急、重要、普通、低',
    handler VARCHAR(50) COMMENT '处理人',
    handle_date TIMESTAMP NULL COMMENT '处理时间',
    handle_result TEXT COMMENT '处理结果',
    description TEXT COMMENT '补充说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '投诉信息表';

-- 8. 企业信息档案表
CREATE TABLE IF NOT EXISTS enterprises (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '企业名称',
    type VARCHAR(50) NOT NULL COMMENT '企业类型',
    license_number VARCHAR(50) NOT NULL UNIQUE COMMENT '营业执照号',
    legal_person VARCHAR(50) NOT NULL COMMENT '法人代表',
    address VARCHAR(200) NOT NULL COMMENT '地址',
    phone VARCHAR(20) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    business_scope TEXT COMMENT '经营范围',
    registration_date DATE COMMENT '注册日期',
    status VARCHAR(20) DEFAULT '正常' COMMENT '状态：正常、注销、吊销',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '企业信息档案表';

-- 插入示例数据

-- 游客示例数据
INSERT INTO tourists (name, id_card, phone, email, address, remarks) VALUES
('张三', '110101199001011234', '***********', '<EMAIL>', '北京市朝阳区', 'VIP客户'),
('李四', '110101199002022345', '***********', '<EMAIL>', '上海市浦东新区', '常客'),
('王五', '110101199003033456', '***********', '<EMAIL>', '广州市天河区', '新客户'),
('赵六', '110101199004044567', '***********', '<EMAIL>', '深圳市南山区', '商务客户'),
('钱七', '110101199005055678', '***********', '<EMAIL>', '杭州市西湖区', '家庭客户');

-- 星级饭店示例数据
INSERT INTO hotels (name, address, phone, email, star_level, room_count, facilities, manager, description) VALUES
('北京国际大酒店', '北京市朝阳区建国门外大街1号', '010-12345678', '<EMAIL>', 5, 300, '游泳池、健身房、会议室、餐厅', '张经理', '五星级豪华酒店'),
('上海花园酒店', '上海市黄浦区南京路100号', '021-87654321', '<EMAIL>', 4, 200, '餐厅、会议室、商务中心', '李经理', '四星级商务酒店'),
('广州珠江宾馆', '广州市越秀区环市东路200号', '020-11111111', '<EMAIL>', 3, 150, '餐厅、停车场', '王经理', '三星级经济型酒店'),
('深圳湾大酒店', '深圳市南山区深圳湾科技生态园', '0755-26881234', '<EMAIL>', 5, 250, '无边泳池、SPA、高尔夫', '赵经理', '五星级科技主题酒店'),
('杭州西湖宾馆', '杭州市西湖区杨公堤15号', '0571-87061234', '<EMAIL>', 4, 180, '湖景房、茶艺馆、园林', '钱经理', '四星级湖景酒店');

-- 旅行社示例数据
INSERT INTO travel_agencies (name, address, phone, email, license_number, manager, business_scope) VALUES
('中国国际旅行社', '北京市东城区王府井大街1号', '010-66666666', '<EMAIL>', 'L-BJ-12345', '赵总', '国内外旅游、商务考察'),
('春秋旅行社', '上海市静安区南京西路500号', '021-55555555', '<EMAIL>', 'L-SH-23456', '钱总', '国内旅游、自由行'),
('南湖国旅', '广州市天河区体育西路300号', '020-33333333', '<EMAIL>', 'L-GD-34567', '孙总', '华南地区旅游'),
('携程旅行社', '上海市长宁区金钟路968号', '021-34567890', '<EMAIL>', 'L-SH-45678', '李总', '在线旅游、定制游'),
('途牛旅游', '南京市玄武区玄武大道699号', '025-84685678', '<EMAIL>', 'L-JS-56789', '马总', '跟团游、自助游');

-- 导游示例数据
INSERT INTO guides (name, id_card, phone, email, license_number, languages, experience, specialties, hire_date) VALUES
('陈导游', '110101198501011111', '***********', '<EMAIL>', 'D-BJ-001', '中文、英文', 10, '历史文化、自然风光', '2015-01-01'),
('刘导游', '110101198602022222', '13900139002', '<EMAIL>', 'D-SH-002', '中文、日文', 8, '城市观光、购物', '2017-03-01'),
('周导游', '110101198703033333', '13900139003', '<EMAIL>', 'D-GD-003', '中文、粤语', 5, '美食文化、民俗风情', '2020-06-01');

-- 公寓示例数据
INSERT INTO apartments (name, address, phone, room_type, room_count, price_per_night, facilities, manager) VALUES
('阳光公寓', '北京市海淀区中关村大街1号', '010-77777777', '一室一厅', 50, 200.00, 'WiFi、空调、厨房', '吴经理'),
('温馨家园', '上海市徐汇区淮海中路200号', '021-88888888', '两室一厅', 30, 350.00, 'WiFi、空调、洗衣机', '郑经理'),
('舒适之家', '广州市番禺区大学城300号', '020-99999999', '单间', 80, 150.00, 'WiFi、空调', '何经理');

-- 投诉示例数据
INSERT INTO complaints (customer_name, customer_phone, customer_email, complaint_type, complaint_content, priority) VALUES
('客户A', '13700137001', '<EMAIL>', '服务质量', '酒店服务态度不好，房间卫生有问题', '重要'),
('客户B', '13700137002', '<EMAIL>', '行程安排', '导游临时更改行程，没有提前通知', '普通'),
('客户C', '***********', '<EMAIL>', '费用问题', '额外收费项目没有事先说明', '紧急'),
('客户D', '***********', '<EMAIL>', '交通问题', '旅游大巴车况不佳，空调不制冷', '普通'),
('客户E', '***********', '<EMAIL>', '住宿问题', '预订的酒店与实际不符，降级严重', '重要');

-- 企业档案示例数据
INSERT INTO enterprises (name, type, license_number, legal_person, address, phone, email, business_scope, registration_date, status, description) VALUES
('北京金山旅游集团', '旅行社', '91110000123456789A', '张金山', '北京市朝阳区建国路88号', '010-85551234', '<EMAIL>', '国内外旅游、酒店管理、交通服务', '2010-05-15', '正常', '大型综合旅游集团'),
('上海东方大酒店', '酒店', '91310000234567890B', '李东方', '上海市浦东新区陆家嘴环路1000号', '021-58881234', '<EMAIL>', '住宿服务、餐饮服务、会议服务', '2008-03-20', '正常', '五星级商务酒店'),
('广州南方运输公司', '交通运输', '91440000345678901C', '王南方', '广州市天河区珠江新城花城大道100号', '020-38881234', '<EMAIL>', '旅游客运、包车服务、机场接送', '2012-08-10', '正常', '专业旅游运输服务'),
('深圳欢乐谷', '景区景点', '91440300456789012D', '赵欢乐', '深圳市南山区侨城西街18号', '0755-26881234', '<EMAIL>', '主题公园运营、娱乐服务、文化演出', '2006-07-01', '正常', '大型主题公园'),
('杭州西湖美食城', '餐饮服务', '91330000567890123E', '钱美食', '杭州市西湖区南山路200号', '0571-87061234', '<EMAIL>', '餐饮服务、特色小吃、宴会承办', '2015-12-01', '正常', '西湖特色餐饮'),
('成都蜀韵旅行社', '旅行社', '91510000678901234F', '刘蜀韵', '成都市锦江区春熙路300号', '028-86661234', '<EMAIL>', '四川旅游、熊猫基地游、美食文化游', '2018-04-15', '正常', '四川特色旅游专家'),
('三亚海景度假村', '酒店', '91460000789012345G', '陈海景', '海南省三亚市亚龙湾国家旅游度假区', '0898-88881234', '<EMAIL>', '度假酒店、海滨娱乐、水上运动', '2005-11-20', '正常', '海滨度假酒店'),
('西安古城文化公司', '景区景点', '91610000890123456H', '周古城', '西安市雁塔区大雁塔南广场1号', '029-85551234', '<EMAIL>', '文化旅游、古迹保护、文物展览', '2013-09-05', '正常', '古城文化旅游'),
('昆明花都运输', '交通运输', '91530000901234567I', '吴花都', '昆明市五华区东风西路100号', '0871-65551234', '<EMAIL>', '旅游包车、机场接送、长途客运', '2016-02-28', '停业', '因违规经营暂停业务'),
('青岛海鲜大酒楼', '餐饮服务', '91370000012345678J', '郑海鲜', '青岛市市南区香港中路50号', '0532-85551234', '<EMAIL>', '海鲜餐饮、宴会服务、外卖配送', '2014-06-10', '正常', '青岛特色海鲜餐厅');

-- 企业档案示例数据
INSERT INTO enterprises (name, type, license_number, legal_person, address, phone, email, business_scope, registration_date) VALUES
('北京旅游发展有限公司', '有限责任公司', '91110000123456789A', '张法人', '北京市朝阳区CBD核心区1号', '010-12121212', '<EMAIL>', '旅游开发、酒店管理', '2010-01-01'),
('上海文旅集团', '股份有限公司', '91310000234567890B', '李法人', '上海市浦东新区陆家嘴金融区2号', '021-23232323', '<EMAIL>', '文化旅游、景区运营', '2008-05-01'),
('广东南粤旅业', '有限责任公司', '91440000345678901C', '王法人', '广州市天河区珠江新城3号', '020-34343434', '<EMAIL>', '旅行社、交通运输', '2012-08-01');
