-- 快速数据库设置脚本
-- 用于修复导游模块问题

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS tourism_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE tourism_system;

-- 检查并创建导游表
CREATE TABLE IF NOT EXISTS guides (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
    phone VARCHAR(11) NOT NULL COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    license_number VARCHAR(50) NOT NULL UNIQUE COMMENT '导游证号',
    languages VARCHAR(200) COMMENT '语言能力',
    experience INT DEFAULT 0 COMMENT '从业年限',
    specialties TEXT COMMENT '专业特长',
    status VARCHAR(20) DEFAULT '在职' COMMENT '状态：在职、离职、休假',
    hire_date DATE COMMENT '入职时间',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '导游信息表';

-- 清空现有数据（如果有的话）
DELETE FROM guides;

-- 插入测试数据
INSERT INTO guides (name, id_card, phone, email, license_number, languages, experience, specialties, hire_date, status, description) VALUES
('陈导游', '110101198501011111', '13900139001', '<EMAIL>', 'D-BJ-001', '中文、英文', 10, '历史文化、自然风光', '2015-01-01', '在职', '资深导游，专业历史文化讲解'),
('刘导游', '110101198602022222', '13900139002', '<EMAIL>', 'D-SH-002', '中文、日文', 8, '城市观光、购物', '2017-03-01', '在职', '擅长城市观光和购物指导'),
('周导游', '110101198703033333', '13900139003', '<EMAIL>', 'D-GD-003', '中文、粤语', 5, '美食文化、民俗风情', '2020-06-01', '在职', '美食文化专家'),
('王导游', '110101198804044444', '13900139004', '<EMAIL>', 'D-BJ-004', '中文、英文、法文', 12, '国际旅游、商务接待', '2012-08-01', '在职', '国际旅游专家，多语言服务'),
('李导游', '110101198905055555', '13900139005', '<EMAIL>', 'D-SZ-005', '中文、英文', 6, '科技旅游、现代建筑', '2019-01-01', '在职', '科技园区和现代建筑专业导游');

-- 验证数据插入
SELECT COUNT(*) as guide_count FROM guides;
SELECT * FROM guides ORDER BY experience DESC;

-- 显示表结构
DESCRIBE guides;
