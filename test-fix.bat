@echo off
echo ========================================
echo 游客模块修复验证脚本
echo ========================================
echo.

echo 1. 检查MySQL服务状态...
net start | findstr -i mysql
if %errorlevel% equ 0 (
    echo [✓] MySQL服务正在运行
) else (
    echo [✗] MySQL服务未运行
    echo 请启动MySQL服务后重试
    pause
    exit /b 1
)
echo.

echo 2. 检查数据库连接...
mysql -u root -proot -e "USE tourism_system; SELECT COUNT(*) as tourist_count FROM tourists;" 2>nul
if %errorlevel% equ 0 (
    echo [✓] 数据库连接成功
) else (
    echo [✗] 数据库连接失败
    echo 请检查数据库配置
    pause
    exit /b 1
)
echo.

echo 3. 检查表结构...
mysql -u root -proot -e "USE tourism_system; DESCRIBE tourists;" | findstr customer_type >nul
if %errorlevel% equ 0 (
    echo [✓] 表结构已更新（包含customer_type字段）
) else (
    echo [✗] 表结构未更新
    pause
    exit /b 1
)
echo.

echo 4. 检查编译文件...
if exist "out\artifacts\webtest_war_exploded\WEB-INF\classes\com\tourism\servlet\TouristServlet.class" (
    echo [✓] TouristServlet已编译
) else (
    echo [✗] TouristServlet未编译
    pause
    exit /b 1
)
echo.

echo 5. 显示游客数据...
mysql -u root -proot -e "USE tourism_system; SELECT id, name, customer_type, preferences FROM tourists LIMIT 3;"
echo.

echo ========================================
echo 修复验证完成！
echo ========================================
echo.
echo 现在您可以：
echo 1. 访问调试页面：http://localhost:8080/webtest/debug-tourist.jsp
echo 2. 访问游客列表：http://localhost:8080/webtest/tourist
echo 3. 添加新游客：http://localhost:8080/webtest/tourist?action=add
echo.
pause
