import java.sql.*;

public class DatabaseChecker {
    private static final String JDBC_URL = "******************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "root";

    public static void main(String[] args) {
        checkDatabase();
    }

    public static void checkDatabase() {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;

        try {
            // 连接数据库
            conn = DriverManager.getConnection(
                    JDBC_URL + "?useSSL=false&serverTimezone=UTC",
                    USERNAME,
                    PASSWORD
            );

            if (conn != null) {
                System.out.println("✅ 数据库连接成功！");
                stmt = conn.createStatement();

                // 检查所有表
                System.out.println("\n📋 检查数据库表：");
                rs = stmt.executeQuery("SHOW TABLES");
                while (rs.next()) {
                    System.out.println("  - " + rs.getString(1));
                }

                // 检查导游表
                System.out.println("\n🧭 检查导游表：");
                try {
                    rs = stmt.executeQuery("SELECT COUNT(*) FROM guides");
                    if (rs.next()) {
                        System.out.println("  导游表记录数: " + rs.getInt(1));
                    }
                    
                    // 显示导游表结构
                    rs = stmt.executeQuery("DESCRIBE guides");
                    System.out.println("  导游表结构:");
                    while (rs.next()) {
                        System.out.println("    " + rs.getString("Field") + " - " + rs.getString("Type"));
                    }
                } catch (SQLException e) {
                    System.out.println("  ❌ 导游表不存在或有问题: " + e.getMessage());
                }

                // 检查游客表
                System.out.println("\n👥 检查游客表：");
                try {
                    rs = stmt.executeQuery("SELECT COUNT(*) FROM tourists");
                    if (rs.next()) {
                        System.out.println("  游客表记录数: " + rs.getInt(1));
                    }
                    
                    // 显示游客表结构
                    rs = stmt.executeQuery("DESCRIBE tourists");
                    System.out.println("  游客表结构:");
                    while (rs.next()) {
                        System.out.println("    " + rs.getString("Field") + " - " + rs.getString("Type"));
                    }
                } catch (SQLException e) {
                    System.out.println("  ❌ 游客表不存在或有问题: " + e.getMessage());
                }

                System.out.println("\n✅ 数据库检查完成！");
            }
        } catch (SQLException e) {
            System.err.println("❌ 数据库操作失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                System.err.println("关闭资源时出错：" + e.getMessage());
            }
        }
    }
}
