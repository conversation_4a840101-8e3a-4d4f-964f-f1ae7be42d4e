package com.tourism.util;

/**
 * 分页工具类
 */
public class PageUtil {
    private int currentPage = 1;        // 当前页码
    private int pageSize = 10;          // 每页显示数量
    private int totalRecords = 0;       // 总记录数
    private int totalPages = 0;         // 总页数
    private int startRecord = 0;        // 起始记录位置
    
    public PageUtil() {}
    
    public PageUtil(int currentPage, int pageSize) {
        this.currentPage = currentPage > 0 ? currentPage : 1;
        this.pageSize = pageSize > 0 ? pageSize : 10;
    }
    
    public PageUtil(int currentPage, int pageSize, int totalRecords) {
        this(currentPage, pageSize);
        this.totalRecords = totalRecords;
        calculatePages();
    }
    
    /**
     * 计算分页信息
     */
    private void calculatePages() {
        if (totalRecords > 0) {
            totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            if (currentPage > totalPages) {
                currentPage = totalPages;
            }
            startRecord = (currentPage - 1) * pageSize;
        }
    }
    
    /**
     * 设置总记录数并重新计算分页信息
     */
    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
        calculatePages();
    }
    
    /**
     * 获取SQL LIMIT子句的起始位置
     */
    public int getOffset() {
        return startRecord;
    }
    
    /**
     * 获取SQL LIMIT子句的数量
     */
    public int getLimit() {
        return pageSize;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return currentPage > 1;
    }
    
    /**
     * 是否有下一页
     */
    public boolean hasNext() {
        return currentPage < totalPages;
    }
    
    /**
     * 获取上一页页码
     */
    public int getPreviousPage() {
        return hasPrevious() ? currentPage - 1 : 1;
    }
    
    /**
     * 获取下一页页码
     */
    public int getNextPage() {
        return hasNext() ? currentPage + 1 : totalPages;
    }
    
    /**
     * 获取页码范围（用于显示页码导航）
     */
    public int[] getPageRange() {
        int start = Math.max(1, currentPage - 2);
        int end = Math.min(totalPages, currentPage + 2);
        
        // 确保显示5个页码（如果总页数足够）
        if (end - start < 4) {
            if (start == 1) {
                end = Math.min(totalPages, start + 4);
            } else {
                start = Math.max(1, end - 4);
            }
        }
        
        int[] range = new int[end - start + 1];
        for (int i = 0; i < range.length; i++) {
            range[i] = start + i;
        }
        return range;
    }
    
    /**
     * 获取显示信息（例如：显示第1-10条，共50条记录）
     */
    public String getDisplayInfo() {
        if (totalRecords == 0) {
            return "暂无数据";
        }
        
        int start = startRecord + 1;
        int end = Math.min(startRecord + pageSize, totalRecords);
        return String.format("显示第%d-%d条，共%d条记录", start, end, totalRecords);
    }
    
    // Getters and Setters
    public int getCurrentPage() {
        return currentPage;
    }
    
    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage > 0 ? currentPage : 1;
        calculatePages();
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize > 0 ? pageSize : 10;
        calculatePages();
    }
    
    public int getTotalRecords() {
        return totalRecords;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public int getStartRecord() {
        return startRecord;
    }
}
