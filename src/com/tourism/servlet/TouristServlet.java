package com.tourism.servlet;

import com.tourism.dao.TouristDAO;
import com.tourism.model.Tourist;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 游客管理Servlet
 */
public class TouristServlet extends HttpServlet {
    private TouristDAO touristDAO = new TouristDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = request.getParameter("action");
        
        if (action == null) {
            action = "list";
        }
        
        switch (action) {
            case "list":
                listTourists(request, response);
                break;
            case "add":
                showAddForm(request, response);
                break;
            case "edit":
                showEditForm(request, response);
                break;
            case "delete":
                deleteTourist(request, response);
                break;
            case "search":
                searchTourists(request, response);
                break;
            default:
                listTourists(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addTourist(request, response);
        } else if ("update".equals(action)) {
            updateTourist(request, response);
        }
    }
    
    /**
     * 显示游客列表
     */
    private void listTourists(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        List<Tourist> tourists = touristDAO.getAllTourists();
        request.setAttribute("tourists", tourists);
        request.getRequestDispatcher("/tourist-list.jsp").forward(request, response);
    }

    /**
     * 显示添加游客表单
     */
    private void showAddForm(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.getRequestDispatcher("/tourist-add.jsp").forward(request, response);
    }
    
    /**
     * 添加游客
     */
    private void addTourist(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String address = request.getParameter("address");
        String remarks = request.getParameter("remarks");
        String customerType = request.getParameter("customerType");
        String preferences = request.getParameter("preferences");
        String notes = request.getParameter("notes");
        String status = request.getParameter("status");

        Tourist tourist = new Tourist(name, idCard, phone, email, address);
        tourist.setRemarks(remarks);
        tourist.setCustomerType(customerType);
        tourist.setPreferences(preferences);
        tourist.setNotes(notes);
        tourist.setStatus(status != null ? status : "活跃");

        boolean success = touristDAO.addTourist(tourist);

        if (success) {
            request.getSession().setAttribute("message", "游客添加成功！");
        } else {
            request.getSession().setAttribute("error", "游客添加失败！");
        }

        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 显示编辑游客表单
     */
    private void showEditForm(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        Tourist tourist = touristDAO.getTouristById(id);
        request.setAttribute("tourist", tourist);
        request.getRequestDispatcher("/tourist-edit.jsp").forward(request, response);
    }
    
    /**
     * 更新游客信息
     */
    private void updateTourist(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        String name = request.getParameter("name");
        String idCard = request.getParameter("idCard");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String address = request.getParameter("address");
        String status = request.getParameter("status");
        String remarks = request.getParameter("remarks");
        String customerType = request.getParameter("customerType");
        String preferences = request.getParameter("preferences");
        String notes = request.getParameter("notes");

        Tourist tourist = new Tourist();
        tourist.setId(id);
        tourist.setName(name);
        tourist.setIdCard(idCard);
        tourist.setPhone(phone);
        tourist.setEmail(email);
        tourist.setAddress(address);
        tourist.setStatus(status);
        tourist.setRemarks(remarks);
        tourist.setCustomerType(customerType);
        tourist.setPreferences(preferences);
        tourist.setNotes(notes);
        
        boolean success = touristDAO.updateTourist(tourist);

        if (success) {
            request.getSession().setAttribute("message", "游客信息更新成功！");
        } else {
            request.getSession().setAttribute("error", "游客信息更新失败！");
        }

        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 删除游客
     */
    private void deleteTourist(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int id = Integer.parseInt(request.getParameter("id"));
        boolean success = touristDAO.deleteTourist(id);
        
        if (success) {
            request.setAttribute("message", "游客删除成功！");
        } else {
            request.setAttribute("error", "游客删除失败！");
        }
        
        response.sendRedirect(request.getContextPath() + "/tourist?action=list");
    }
    
    /**
     * 搜索游客
     */
    private void searchTourists(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String keyword = request.getParameter("keyword");
        String phone = request.getParameter("phone");
        String customerType = request.getParameter("customerType");

        List<Tourist> tourists;

        // 如果有任何搜索条件，使用多条件搜索
        if ((keyword != null && !keyword.trim().isEmpty()) ||
            (phone != null && !phone.trim().isEmpty()) ||
            (customerType != null && !customerType.trim().isEmpty())) {
            tourists = touristDAO.searchTourists(keyword, phone, customerType);
        } else {
            tourists = touristDAO.getAllTourists();
        }

        request.setAttribute("tourists", tourists);
        request.setAttribute("keyword", keyword);
        request.setAttribute("phone", phone);
        request.setAttribute("customerType", customerType);
        request.getRequestDispatcher("/tourist-list.jsp").forward(request, response);
    }
}
