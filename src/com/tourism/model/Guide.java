package com.tourism.model;

import java.util.Date;

/**
 * 导游实体类
 */
public class Guide {
    private int id;
    private String name;
    private String idCard;
    private String phone;
    private String email;
    private String licenseNumber; // 导游证号
    private String languages; // 语言能力
    private int experience; // 从业年限
    private String specialties; // 专业特长
    private String status; // 在职、离职、休假
    private Date hireDate; // 入职时间
    private String description; // 描述
    private String guideType; // 导游类型：资深导游、普通导游、实习导游
    private String serviceArea; // 服务区域
    private String certifications; // 资质证书
    
    public Guide() {
        this.hireDate = new Date();
        this.status = "在职";
        this.guideType = "普通导游";
    }
    
    public Guide(String name, String idCard, String phone, String email, String licenseNumber) {
        this();
        this.name = name;
        this.idCard = idCard;
        this.phone = phone;
        this.email = email;
        this.licenseNumber = licenseNumber;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getIdCard() {
        return idCard;
    }
    
    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getLicenseNumber() {
        return licenseNumber;
    }
    
    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }
    
    public String getLanguages() {
        return languages;
    }
    
    public void setLanguages(String languages) {
        this.languages = languages;
    }
    
    public int getExperience() {
        return experience;
    }
    
    public void setExperience(int experience) {
        this.experience = experience;
    }
    
    public String getSpecialties() {
        return specialties;
    }
    
    public void setSpecialties(String specialties) {
        this.specialties = specialties;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getHireDate() {
        return hireDate;
    }
    
    public void setHireDate(Date hireDate) {
        this.hireDate = hireDate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getGuideType() {
        return guideType;
    }
    
    public void setGuideType(String guideType) {
        this.guideType = guideType;
    }
    
    public String getServiceArea() {
        return serviceArea;
    }
    
    public void setServiceArea(String serviceArea) {
        this.serviceArea = serviceArea;
    }
    
    public String getCertifications() {
        return certifications;
    }
    
    public void setCertifications(String certifications) {
        this.certifications = certifications;
    }
    
    @Override
    public String toString() {
        return "Guide{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", idCard='" + idCard + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", licenseNumber='" + licenseNumber + '\'' +
                ", languages='" + languages + '\'' +
                ", experience=" + experience +
                ", specialties='" + specialties + '\'' +
                ", status='" + status + '\'' +
                ", hireDate=" + hireDate +
                ", description='" + description + '\'' +
                ", guideType='" + guideType + '\'' +
                ", serviceArea='" + serviceArea + '\'' +
                ", certifications='" + certifications + '\'' +
                '}';
    }
}
