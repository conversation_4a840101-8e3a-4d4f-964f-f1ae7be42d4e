# Database Connection Configuration
# MySQL Database Settings

# Basic Configuration
db.driver=com.mysql.cj.jdbc.Driver
db.url=******************************************************************************************************************************
db.username=root
db.password=root

# Connection Pool Settings
db.initialSize=5
db.maxActive=20
db.maxIdle=10
db.minIdle=5
db.maxWait=60000

# Database Name
db.name=tourism_system

# Alternative Password Settings (uncomment if needed)
# db.password=root
# db.password=mysql
# db.password=admin
# db.password=123456

# Alternative URL Settings (uncomment if needed)
# db.url=*************************************************************************************************
# db.url=*************************************************************************************************
