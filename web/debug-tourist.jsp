<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.dao.TouristDAO" %>
<%@ page import="com.tourism.model.Tourist" %>
<%@ page import="com.tourism.util.DatabaseUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.sql.*" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客模块调试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-bug text-danger me-2"></i>游客模块调试</h2>
                <p class="text-muted">诊断游客模块的问题</p>
                
                <div class="row">
                    <!-- 数据库连接测试 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-database me-2"></i>数据库连接测试</h5>
                            </div>
                            <div class="card-body">
                                <%
                                    boolean dbConnected = false;
                                    String dbError = "";
                                    try {
                                        Connection conn = DatabaseUtil.getConnection();
                                        if (conn != null && !conn.isClosed()) {
                                            dbConnected = true;
                                            conn.close();
                                        }
                                    } catch (Exception e) {
                                        dbError = e.getMessage();
                                    }
                                %>
                                
                                <% if (dbConnected) { %>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>数据库连接成功！
                                    </div>
                                <% } else { %>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>数据库连接失败！
                                        <br><small><%= dbError %></small>
                                    </div>
                                <% } %>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 表结构检查 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-table me-2"></i>表结构检查</h5>
                            </div>
                            <div class="card-body">
                                <%
                                    boolean tableExists = false;
                                    String tableError = "";
                                    String[] expectedColumns = {"id", "name", "id_card", "phone", "email", "address", "register_date", "status", "remarks", "customer_type", "preferences", "notes"};
                                    java.util.Set<String> actualColumns = new java.util.HashSet<>();
                                    
                                    try {
                                        Connection conn = DatabaseUtil.getConnection();
                                        DatabaseMetaData metaData = conn.getMetaData();
                                        ResultSet rs = metaData.getColumns(null, null, "tourists", null);
                                        
                                        while (rs.next()) {
                                            actualColumns.add(rs.getString("COLUMN_NAME").toLowerCase());
                                        }
                                        
                                        if (!actualColumns.isEmpty()) {
                                            tableExists = true;
                                        }
                                        
                                        rs.close();
                                        conn.close();
                                    } catch (Exception e) {
                                        tableError = e.getMessage();
                                    }
                                %>
                                
                                <% if (tableExists) { %>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>tourists表存在！
                                    </div>
                                    
                                    <h6>字段检查：</h6>
                                    <ul class="list-group list-group-flush">
                                        <% for (String col : expectedColumns) { %>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <%= col %>
                                                <% if (actualColumns.contains(col.toLowerCase())) { %>
                                                    <span class="badge bg-success">✓</span>
                                                <% } else { %>
                                                    <span class="badge bg-danger">✗</span>
                                                <% } %>
                                            </li>
                                        <% } %>
                                    </ul>
                                <% } else { %>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>tourists表不存在！
                                        <br><small><%= tableError %></small>
                                    </div>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- DAO测试 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-code me-2"></i>DAO功能测试</h5>
                            </div>
                            <div class="card-body">
                                <%
                                    boolean daoWorking = false;
                                    String daoError = "";
                                    List<Tourist> tourists = null;
                                    int touristCount = 0;
                                    
                                    try {
                                        TouristDAO touristDAO = new TouristDAO();
                                        tourists = touristDAO.getAllTourists();
                                        touristCount = tourists.size();
                                        daoWorking = true;
                                    } catch (Exception e) {
                                        daoError = e.getMessage();
                                        e.printStackTrace();
                                    }
                                %>
                                
                                <% if (daoWorking) { %>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>DAO功能正常！
                                        <br>找到 <strong><%= touristCount %></strong> 位游客
                                    </div>
                                    
                                    <% if (touristCount > 0) { %>
                                        <h6>游客数据示例：</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>姓名</th>
                                                        <th>电话</th>
                                                        <th>客户类型</th>
                                                        <th>状态</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <% for (int i = 0; i < Math.min(3, tourists.size()); i++) { 
                                                        Tourist t = tourists.get(i);
                                                    %>
                                                        <tr>
                                                            <td><%= t.getId() %></td>
                                                            <td><%= t.getName() %></td>
                                                            <td><%= t.getPhone() %></td>
                                                            <td><%= t.getCustomerType() != null ? t.getCustomerType() : "未设置" %></td>
                                                            <td><%= t.getStatus() != null ? t.getStatus() : "未设置" %></td>
                                                        </tr>
                                                    <% } %>
                                                </tbody>
                                            </table>
                                        </div>
                                    <% } else { %>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-info-circle me-2"></i>数据库中暂无游客数据
                                        </div>
                                    <% } %>
                                <% } else { %>
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>DAO功能异常！
                                        <br><small><%= daoError %></small>
                                    </div>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tools me-2"></i>快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="btn-group me-2" role="group">
                                    <a href="<%= request.getContextPath() %>/tourist" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>游客列表
                                    </a>
                                    <a href="<%= request.getContextPath() %>/tourist?action=add" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>添加游客
                                    </a>
                                </div>
                                
                                <div class="btn-group" role="group">
                                    <a href="<%= request.getContextPath() %>/test-database.jsp" class="btn btn-info">
                                        <i class="fas fa-database me-1"></i>数据库测试
                                    </a>
                                    <a href="<%= request.getContextPath() %>/" class="btn btn-secondary">
                                        <i class="fas fa-home me-1"></i>返回首页
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
