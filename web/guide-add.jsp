<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加导游 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .form-container { background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .required { color: red; }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-map-marked-alt me-2"></i>旅游管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="${pageContext.request.contextPath}/guide">返回导游列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="form-container p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4><i class="fas fa-user-tie me-2"></i>添加导游</h4>
                        <a href="${pageContext.request.contextPath}/guide" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回列表
                        </a>
                    </div>

                    <!-- 错误消息 -->
                    <%
                        String error = (String) session.getAttribute("error");
                        if (error != null) {
                    %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <%
                            session.removeAttribute("error");
                        }
                    %>

                    <form method="post" action="${pageContext.request.contextPath}/guide" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-user me-2"></i>基本信息</h6>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">姓名 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入导游姓名</div>
                                </div>

                                <div class="mb-3">
                                    <label for="idCard" class="form-label">身份证号 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="idCard" name="idCard" 
                                           pattern="[0-9]{17}[0-9Xx]" maxlength="18" required>
                                    <div class="invalid-feedback">请输入正确的18位身份证号</div>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">联系电话 <span class="required">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           pattern="[0-9]{11}" maxlength="11" required>
                                    <div class="invalid-feedback">请输入正确的11位手机号</div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱地址</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                    <div class="invalid-feedback">请输入正确的邮箱地址</div>
                                </div>

                                <div class="mb-3">
                                    <label for="licenseNumber" class="form-label">导游证号 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="licenseNumber" name="licenseNumber" required>
                                    <div class="invalid-feedback">请输入导游证号</div>
                                </div>
                            </div>

                            <!-- 专业信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-briefcase me-2"></i>专业信息</h6>
                                
                                <div class="mb-3">
                                    <label for="languages" class="form-label">语言能力</label>
                                    <input type="text" class="form-control" id="languages" name="languages" 
                                           placeholder="例如：中文、英文、日文">
                                    <div class="form-text">请用逗号分隔多种语言</div>
                                </div>

                                <div class="mb-3">
                                    <label for="experience" class="form-label">从业年限</label>
                                    <input type="number" class="form-control" id="experience" name="experience" 
                                           min="0" max="50" value="0">
                                    <div class="form-text">请输入从业年数</div>
                                </div>

                                <div class="mb-3">
                                    <label for="specialties" class="form-label">专业特长</label>
                                    <textarea class="form-control" id="specialties" name="specialties" rows="3" 
                                              placeholder="例如：历史文化、自然风光、美食文化等"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">工作状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="在职" selected>在职</option>
                                        <option value="休假">休假</option>
                                        <option value="离职">离职</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">备注描述</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" 
                                              placeholder="其他补充信息"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="${pageContext.request.contextPath}/guide" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>保存导游
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Bootstrap 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 身份证号格式化
        document.getElementById('idCard').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            e.target.value = value;
        });

        // 手机号格式验证
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });
    </script>
</body>
</html>
