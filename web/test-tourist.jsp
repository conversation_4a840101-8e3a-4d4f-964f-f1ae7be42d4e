<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.tourism.dao.TouristDAO" %>
<%@ page import="com.tourism.model.Tourist" %>
<%@ page import="java.util.List" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客模块测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-vial text-primary me-2"></i>游客模块测试</h2>
                <p class="text-muted">测试游客模块的数据库连接和基本功能</p>
                
                <div class="card">
                    <div class="card-body">
                        <%
                            try {
                                TouristDAO touristDAO = new TouristDAO();
                                
                                // 测试查询所有游客
                                List<Tourist> tourists = touristDAO.getAllTourists();
                                
                                out.println("<div class='alert alert-success'>");
                                out.println("<i class='fas fa-check-circle me-2'></i>");
                                out.println("<strong>数据库连接成功！</strong>");
                                out.println("</div>");
                                
                                out.println("<h5>游客数据测试结果：</h5>");
                                out.println("<ul class='list-group'>");
                                out.println("<li class='list-group-item d-flex justify-content-between align-items-center'>");
                                out.println("总游客数量");
                                out.println("<span class='badge bg-primary rounded-pill'>" + tourists.size() + "</span>");
                                out.println("</li>");
                                
                                if (!tourists.isEmpty()) {
                                    out.println("<li class='list-group-item'>");
                                    out.println("<strong>最新游客信息：</strong><br>");
                                    Tourist latestTourist = tourists.get(0);
                                    out.println("姓名: " + latestTourist.getName() + "<br>");
                                    out.println("电话: " + latestTourist.getPhone() + "<br>");
                                    out.println("客户类型: " + (latestTourist.getCustomerType() != null ? latestTourist.getCustomerType() : "未设置") + "<br>");
                                    out.println("状态: " + (latestTourist.getStatus() != null ? latestTourist.getStatus() : "未设置") + "<br>");
                                    out.println("</li>");
                                }
                                
                                out.println("</ul>");
                                
                                // 测试字段完整性
                                out.println("<h5 class='mt-4'>字段完整性测试：</h5>");
                                out.println("<div class='row'>");
                                
                                boolean hasCustomerType = false;
                                boolean hasPreferences = false;
                                boolean hasNotes = false;
                                
                                for (Tourist tourist : tourists) {
                                    if (tourist.getCustomerType() != null) hasCustomerType = true;
                                    if (tourist.getPreferences() != null) hasPreferences = true;
                                    if (tourist.getNotes() != null) hasNotes = true;
                                }
                                
                                out.println("<div class='col-md-4'>");
                                out.println("<div class='card " + (hasCustomerType ? "border-success" : "border-warning") + "'>");
                                out.println("<div class='card-body text-center'>");
                                out.println("<i class='fas fa-tags fa-2x " + (hasCustomerType ? "text-success" : "text-warning") + "'></i>");
                                out.println("<h6 class='mt-2'>客户类型字段</h6>");
                                out.println("<span class='badge " + (hasCustomerType ? "bg-success" : "bg-warning") + "'>");
                                out.println(hasCustomerType ? "正常" : "需要数据");
                                out.println("</span>");
                                out.println("</div></div></div>");
                                
                                out.println("<div class='col-md-4'>");
                                out.println("<div class='card " + (hasPreferences ? "border-success" : "border-warning") + "'>");
                                out.println("<div class='card-body text-center'>");
                                out.println("<i class='fas fa-heart fa-2x " + (hasPreferences ? "text-success" : "text-warning") + "'></i>");
                                out.println("<h6 class='mt-2'>旅游偏好字段</h6>");
                                out.println("<span class='badge " + (hasPreferences ? "bg-success" : "bg-warning") + "'>");
                                out.println(hasPreferences ? "正常" : "需要数据");
                                out.println("</span>");
                                out.println("</div></div></div>");
                                
                                out.println("<div class='col-md-4'>");
                                out.println("<div class='card " + (hasNotes ? "border-success" : "border-warning") + "'>");
                                out.println("<div class='card-body text-center'>");
                                out.println("<i class='fas fa-sticky-note fa-2x " + (hasNotes ? "text-success" : "text-warning") + "'></i>");
                                out.println("<h6 class='mt-2'>备注字段</h6>");
                                out.println("<span class='badge " + (hasNotes ? "bg-success" : "bg-warning") + "'>");
                                out.println(hasNotes ? "正常" : "需要数据");
                                out.println("</span>");
                                out.println("</div></div></div>");
                                
                                out.println("</div>");
                                
                            } catch (Exception e) {
                                out.println("<div class='alert alert-danger'>");
                                out.println("<i class='fas fa-exclamation-triangle me-2'></i>");
                                out.println("<strong>错误：</strong> " + e.getMessage());
                                out.println("</div>");
                                
                                out.println("<div class='alert alert-info'>");
                                out.println("<h6>可能的解决方案：</h6>");
                                out.println("<ol>");
                                out.println("<li>确保数据库服务正在运行</li>");
                                out.println("<li>检查数据库连接配置</li>");
                                out.println("<li>运行数据库初始化脚本</li>");
                                out.println("<li>运行表结构更新脚本</li>");
                                out.println("</ol>");
                                out.println("</div>");
                            }
                        %>
                        
                        <div class="mt-4">
                            <h5>快速操作：</h5>
                            <div class="btn-group" role="group">
                                <a href="<%= request.getContextPath() %>/tourist" class="btn btn-primary">
                                    <i class="fas fa-list me-1"></i>游客列表
                                </a>
                                <a href="<%= request.getContextPath() %>/tourist?action=add" class="btn btn-success">
                                    <i class="fas fa-plus me-1"></i>添加游客
                                </a>
                                <a href="<%= request.getContextPath() %>/" class="btn btn-secondary">
                                    <i class="fas fa-home me-1"></i>返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
