<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Servlet测试 - 旅游管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-cogs text-primary me-2"></i>Servlet连接测试</h2>
                <p class="text-muted">测试各个Servlet的连接状态</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>游客模块测试</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="<%= request.getContextPath() %>/tourist" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>测试游客列表 (GET)
                                    </a>
                                    <a href="<%= request.getContextPath() %>/tourist?action=add" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>测试添加页面 (GET)
                                    </a>
                                    <a href="<%= request.getContextPath() %>/tourist?action=search&keyword=张" class="btn btn-info">
                                        <i class="fas fa-search me-1"></i>测试搜索功能 (GET)
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>POST测试表单：</h6>
                                <form method="post" action="<%= request.getContextPath() %>/tourist">
                                    <input type="hidden" name="action" value="add">
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" name="name" placeholder="姓名" value="测试用户" required>
                                    </div>
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" name="idCard" placeholder="身份证号" value="110101199901010001" required>
                                    </div>
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" name="phone" placeholder="电话" value="13900000001" required>
                                    </div>
                                    <div class="mb-2">
                                        <input type="email" class="form-control form-control-sm" name="email" placeholder="邮箱" value="<EMAIL>">
                                    </div>
                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" name="customerType" required>
                                            <option value="新客户">新客户</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-paper-plane me-1"></i>测试添加游客 (POST)
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Context Path:</strong></td>
                                        <td><code><%= request.getContextPath() %></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Server Info:</strong></td>
                                        <td><%= application.getServerInfo() %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Servlet Version:</strong></td>
                                        <td><%= application.getMajorVersion() %>.<%= application.getMinorVersion() %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Request URL:</strong></td>
                                        <td><code><%= request.getRequestURL() %></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Session ID:</strong></td>
                                        <td><code><%= session.getId() %></code></td>
                                    </tr>
                                </table>
                                
                                <h6 class="mt-3">预期的Servlet URL：</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <code><%= request.getScheme() %>://<%= request.getServerName() %>:<%= request.getServerPort() %><%= request.getContextPath() %>/tourist</code>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他模块测试 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-link me-2"></i>其他模块连接测试</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="d-grid">
                                            <a href="<%= request.getContextPath() %>/hotel" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-hotel me-1"></i>饭店模块
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-grid">
                                            <a href="<%= request.getContextPath() %>/guide" class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-user-tie me-1"></i>导游模块
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-grid">
                                            <a href="<%= request.getContextPath() %>/agency" class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-building me-1"></i>旅行社模块
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-grid">
                                            <a href="<%= request.getContextPath() %>/apartment" class="btn btn-outline-warning btn-sm">
                                                <i class="fas fa-home me-1"></i>公寓模块
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 返回按钮 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <a href="<%= request.getContextPath() %>/debug-tourist.jsp" class="btn btn-secondary">
                                <i class="fas fa-bug me-1"></i>调试页面
                            </a>
                            <a href="<%= request.getContextPath() %>/" class="btn btn-primary">
                                <i class="fas fa-home me-1"></i>返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
