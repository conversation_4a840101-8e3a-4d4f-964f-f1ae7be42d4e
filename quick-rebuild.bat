@echo off
echo ========================================
echo 🔄 Java Web项目快速重建脚本
echo ========================================
echo.

echo 📁 创建输出目录...
mkdir out\production\webtest 2>nul
mkdir out\artifacts\webtest_war_exploded\WEB-INF\classes 2>nul
mkdir out\artifacts\webtest_war_exploded\WEB-INF\lib 2>nul

echo.
echo ☕ 编译Java源文件...
echo 编译路径: src -> out\production\webtest

javac -cp "lib\*;web\WEB-INF\lib\*" -d out\production\webtest src\com\tourism\model\*.java
if %errorlevel% neq 0 (
    echo ❌ 模型类编译失败！
    pause
    exit /b 1
)

javac -cp "lib\*;web\WEB-INF\lib\*;out\production\webtest" -d out\production\webtest src\com\tourism\util\*.java
if %errorlevel% neq 0 (
    echo ❌ 工具类编译失败！
    pause
    exit /b 1
)

javac -cp "lib\*;web\WEB-INF\lib\*;out\production\webtest" -d out\production\webtest src\com\tourism\dao\*.java
if %errorlevel% neq 0 (
    echo ❌ DAO类编译失败！
    pause
    exit /b 1
)

javac -cp "lib\*;web\WEB-INF\lib\*;out\production\webtest" -d out\production\webtest src\com\tourism\filter\*.java
if %errorlevel% neq 0 (
    echo ❌ 过滤器类编译失败！
    pause
    exit /b 1
)

javac -cp "lib\*;web\WEB-INF\lib\*;out\production\webtest" -d out\production\webtest src\com\tourism\servlet\*.java
if %errorlevel% neq 0 (
    echo ❌ Servlet类编译失败！
    pause
    exit /b 1
)

javac -cp "lib\*;web\WEB-INF\lib\*;out\production\webtest" -d out\production\webtest src\*.java
if %errorlevel% neq 0 (
    echo ⚠️ 其他类编译可能有问题，但继续...
)

echo ✅ Java编译完成！

echo.
echo 📦 复制文件到部署目录...

:: 复制编译后的类文件
xcopy /E /I /Y out\production\webtest\* out\artifacts\webtest_war_exploded\WEB-INF\classes\

:: 复制web资源
xcopy /E /I /Y web\* out\artifacts\webtest_war_exploded\

:: 复制库文件
xcopy /Y lib\*.jar out\artifacts\webtest_war_exploded\WEB-INF\lib\

:: 复制配置文件
copy src\database.properties out\artifacts\webtest_war_exploded\WEB-INF\classes\

echo ✅ 文件复制完成！

echo.
echo 🔍 验证关键文件...
if exist "out\artifacts\webtest_war_exploded\WEB-INF\classes\com\tourism\servlet\GuideServlet.class" (
    echo ✅ GuideServlet.class 存在
) else (
    echo ❌ GuideServlet.class 不存在！
)

if exist "out\artifacts\webtest_war_exploded\WEB-INF\web.xml" (
    echo ✅ web.xml 存在
) else (
    echo ❌ web.xml 不存在！
)

if exist "out\artifacts\webtest_war_exploded\WEB-INF\lib\mysql-connector-j-9.2.0.jar" (
    echo ✅ MySQL驱动 存在
) else (
    echo ❌ MySQL驱动 不存在！
)

echo.
echo 🎯 构建完成！
echo.
echo 📋 下一步操作：
echo 1. 在IntelliJ IDEA中重新启动应用服务器
echo 2. 或者如果使用外部Tomcat，重启Tomcat服务
echo 3. 访问: http://localhost:8080/webtest_war_exploded/guide
echo.
echo 🔧 如果仍有问题，请检查：
echo - 应用服务器是否完全重启
echo - 端口8080是否被占用
echo - MySQL服务是否运行
echo.
pause
