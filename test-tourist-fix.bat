@echo off
echo ========================================
echo 🧪 游客模块修复测试脚本
echo ========================================
echo.

echo 📋 测试说明：
echo 1. 确保应用服务器正在运行
echo 2. 确保MySQL数据库连接正常
echo 3. 本脚本将打开浏览器测试游客模块功能
echo.

echo 🔍 检查关键文件...
if exist "out\artifacts\webtest_war_exploded\WEB-INF\classes\com\tourism\servlet\TouristServlet.class" (
    echo ✅ TouristServlet.class 存在
) else (
    echo ❌ TouristServlet.class 不存在！
    pause
    exit /b 1
)

if exist "out\artifacts\webtest_war_exploded\tourist-add.jsp" (
    echo ✅ tourist-add.jsp 存在
) else (
    echo ❌ tourist-add.jsp 不存在！
    pause
    exit /b 1
)

if exist "out\artifacts\webtest_war_exploded\tourist-edit.jsp" (
    echo ✅ tourist-edit.jsp 存在
) else (
    echo ❌ tourist-edit.jsp 不存在！
    pause
    exit /b 1
)

echo.
echo 🌐 打开浏览器进行测试...
echo.

echo 📝 测试步骤：
echo 1. 游客列表页面
start http://localhost:8080/webtest_war_exploded/tourist

echo.
echo 2. 新增游客页面（应该能正常显示）
start http://localhost:8080/webtest_war_exploded/tourist?action=add

echo.
echo 3. 编辑游客页面（需要先有游客数据，可以手动测试）
echo    URL格式: http://localhost:8080/webtest_war_exploded/tourist?action=edit&id=1

echo.
echo 🔧 修复内容：
echo - 修正了TouristServlet中showAddForm方法的JSP路径
echo - 修正了TouristServlet中showEditForm方法的JSP路径
echo - 从 /add.jsp 改为 /tourist-add.jsp
echo - 从 /edit.jsp 改为 /tourist-edit.jsp

echo.
echo 📊 预期结果：
echo ✅ 新增游客页面应该能正常显示表单
echo ✅ 编辑游客页面应该能正常显示表单（有数据时）
echo ✅ 不再出现404错误

echo.
echo 🚨 如果仍有问题，请检查：
echo - 应用服务器是否完全重启
echo - 浏览器缓存是否清除
echo - 数据库连接是否正常

echo.
pause
