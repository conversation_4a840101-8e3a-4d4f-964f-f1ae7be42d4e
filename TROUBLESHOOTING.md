# 游客模块问题诊断与解决方案

## 🔍 当前问题分析

根据您提供的错误截图，问题是：
- **HTTP ERROR 404** - 找不到页面
- URL: `http://localhost:8080/webtest_war_exploded/tourist?action=add`
- URL: `http://localhost:8080/webtest_war_exploded/tourist?action=edit&id=1`

## 🎯 问题根源

**主要问题：Tomcat服务器未运行**

通过检查发现：
1. ✅ 数据库连接正常 - MySQL服务运行中
2. ✅ 数据库表结构已修复 - 包含所有必要字段
3. ✅ 代码已编译 - 所有类文件存在
4. ✅ 部署文件已同步 - JSP和类文件都在正确位置
5. ❌ **Tomcat服务器未运行** - 端口8080未监听

## 🚀 解决方案

### 方案1：使用IntelliJ IDEA启动（推荐）

1. **打开IntelliJ IDEA**
2. **配置Tomcat运行配置**：
   - 点击右上角的运行配置下拉菜单
   - 选择 "Edit Configurations..."
   - 添加新的 "Tomcat Server" -> "Local"
   - 配置Tomcat安装路径
   - 在 "Deployment" 标签页添加 `webtest:war exploded` artifact
   - 设置Application context为 `/webtest`

3. **启动服务器**：
   - 点击绿色运行按钮
   - 等待服务器启动完成

### 方案2：使用命令行启动Tomcat

如果您已经安装了Tomcat：

```bash
# 1. 设置环境变量（如果未设置）
set CATALINA_HOME=C:\path\to\tomcat
set JAVA_HOME=C:\path\to\java

# 2. 启动Tomcat
%CATALINA_HOME%\bin\startup.bat

# 3. 部署项目
# 将 out\artifacts\webtest_war_exploded 复制到 %CATALINA_HOME%\webapps\webtest
```

### 方案3：使用其他Web服务器

如果您有其他Web服务器（如Apache、Nginx等），可以：
1. 将 `out\artifacts\webtest_war_exploded` 目录作为Web根目录
2. 配置JSP支持
3. 启动服务器

## 📋 启动后的测试步骤

### 1. 基础连接测试
访问：`http://localhost:8080/webtest/servlet-test.jsp`

### 2. 游客模块测试
- **列表页面**：`http://localhost:8080/webtest/tourist`
- **添加页面**：`http://localhost:8080/webtest/tourist?action=add`
- **调试页面**：`http://localhost:8080/webtest/debug-tourist.jsp`

### 3. 预期结果
- ✅ 游客列表显示3位测试用户
- ✅ 客户类型显示为彩色徽章
- ✅ 添加功能正常工作
- ✅ 编辑功能正常工作

## 🔧 常见问题解决

### 问题1：端口冲突
```bash
# 检查端口占用
netstat -ano | findstr :8080

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 问题2：Java环境问题
```bash
# 检查Java版本
java -version

# 设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-版本号
```

### 问题3：MySQL连接问题
```bash
# 检查MySQL服务
net start | findstr mysql

# 启动MySQL服务
net start mysql80
```

### 问题4：编译问题
```bash
# 重新编译项目
javac -cp "lib/*" -d out/production/webtest src/com/tourism/**/*.java

# 同步到部署目录
xcopy /E /Y "out\production\webtest\*" "out\artifacts\webtest_war_exploded\WEB-INF\classes\"
```

## 📁 项目文件结构验证

确保以下文件存在：

```
out/artifacts/webtest_war_exploded/
├── WEB-INF/
│   ├── web.xml                    ✅ Servlet配置
│   ├── classes/
│   │   ├── com/tourism/servlet/
│   │   │   └── TouristServlet.class ✅ 游客Servlet
│   │   ├── com/tourism/dao/
│   │   │   └── TouristDAO.class     ✅ 数据访问层
│   │   └── database.properties      ✅ 数据库配置
│   └── lib/
│       └── mysql-connector-j-*.jar  ✅ MySQL驱动
├── tourist-list.jsp               ✅ 游客列表页面
├── tourist-add.jsp                ✅ 添加游客页面
├── tourist-edit.jsp               ✅ 编辑游客页面
├── debug-tourist.jsp              ✅ 调试页面
└── servlet-test.jsp               ✅ Servlet测试页面
```

## 🎯 快速启动脚本

运行 `start-tomcat.bat` 脚本进行自动检查和启动指导。

## 📞 如果问题仍然存在

1. **检查IntelliJ IDEA控制台**是否有错误信息
2. **查看Tomcat日志**（通常在 `logs/catalina.out`）
3. **确认项目配置**是否正确
4. **重新导入项目**并重新配置运行环境

## ✅ 成功标志

当一切正常时，您应该能够：
- 访问游客列表并看到3位测试用户
- 成功添加新游客
- 编辑现有游客信息
- 使用搜索功能

---

**注意**：确保在启动Tomcat之前，MySQL服务已经运行，并且数据库表结构已经更新。
