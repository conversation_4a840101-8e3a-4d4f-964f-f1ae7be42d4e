@echo off
chcp 65001 >nul
echo ========================================
echo 启动Tomcat服务器
echo ========================================
echo.

echo 1. 检查Java环境...
java -version 2>nul
if %errorlevel% neq 0 (
    echo [错误] Java未安装或未配置环境变量
    echo 请安装Java并配置JAVA_HOME环境变量
    pause
    exit /b 1
)
echo [✓] Java环境正常

echo.
echo 2. 检查项目编译状态...
if exist "out\artifacts\webtest_war_exploded\WEB-INF\classes\com\tourism\servlet\TouristServlet.class" (
    echo [✓] 项目已编译
) else (
    echo [!] 项目未编译，正在编译...
    javac -cp "lib/*" -d out/production/webtest src/com/tourism/model/*.java src/com/tourism/util/*.java src/com/tourism/dao/*.java src/com/tourism/servlet/*.java src/com/tourism/filter/*.java
    xcopy /E /Y "out\production\webtest\*" "out\artifacts\webtest_war_exploded\WEB-INF\classes\"
    echo [✓] 编译完成
)

echo.
echo 3. 检查MySQL服务...
net start | findstr -i mysql >nul
if %errorlevel% equ 0 (
    echo [✓] MySQL服务正在运行
) else (
    echo [!] MySQL服务未运行，尝试启动...
    net start mysql80 2>nul
    if %errorlevel% equ 0 (
        echo [✓] MySQL服务启动成功
    ) else (
        echo [!] MySQL服务启动失败，请手动启动
    )
)

echo.
echo 4. 检查端口占用...
netstat -an | findstr :8080 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo [!] 端口8080已被占用
    echo 正在查找占用进程...
    netstat -ano | findstr :8080 | findstr LISTENING
    echo 如果需要，请手动结束占用进程
    pause
) else (
    echo [✓] 端口8080可用
)

echo.
echo 5. 启动说明...
echo 由于我无法直接启动您的Tomcat服务器，请按照以下步骤操作：
echo.
echo 方法1 - 使用IntelliJ IDEA：
echo   1. 在IntelliJ IDEA中打开项目
echo   2. 点击右上角的运行配置下拉菜单
echo   3. 选择Tomcat配置并点击运行按钮
echo.
echo 方法2 - 使用命令行（如果已配置Tomcat）：
echo   1. 设置CATALINA_HOME环境变量指向Tomcat安装目录
echo   2. 运行: %%CATALINA_HOME%%\bin\startup.bat
echo   3. 将项目部署到webapps目录
echo.
echo 方法3 - 使用内置服务器：
echo   如果您有其他Web服务器，可以将out\artifacts\webtest_war_exploded目录
echo   作为Web根目录进行部署
echo.

echo ========================================
echo 部署信息
echo ========================================
echo 项目路径: %CD%
echo 部署目录: %CD%\out\artifacts\webtest_war_exploded
echo 访问地址: http://localhost:8080/webtest_war_exploded/
echo 游客模块: http://localhost:8080/webtest_war_exploded/tourist
echo 测试页面: http://localhost:8080/webtest_war_exploded/servlet-test.jsp
echo 调试页面: http://localhost:8080/webtest_war_exploded/debug-tourist.jsp
echo ========================================
echo.

echo 启动Tomcat后，请访问上述地址进行测试
pause
